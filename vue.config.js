const path = require('path')
const { dependencies, actualVersion } = require('./package.json')

const resolve = (dir) => {
	return path.join(__dirname, dir)
}
const whiteListedModules = ['vue']

const BASE_URL = process.env.NODE_ENV === 'production' ? './' : '/'

const config = {
	publicPath: BASE_URL,
	outputDir: 'dist',
	filenameHashing: true,
	crossorigin: '',
	// 设为false打包时不生成.map文件
	productionSourceMap: false,
	pages: {
		index: {
			entry: 'src/main.js',
			template: 'public/index.html',
			filename: 'index.html',
			title: '附物管理终端',
			externals: [...Object.keys(dependencies || {}).filter((d) => !whiteListedModules.includes(d))],
			chunks: ['chunk-vendors', 'chunk-common', 'index']
		},
		web: {
			entry: 'src/web/main.js',
			template: 'public/web.html',
			filename: 'web.html',
			title: '附物管理终端',
			externals: [...Object.keys(dependencies || {}).filter((d) => !whiteListedModules.includes(d))],
			chunks: ['chunk-vendors', 'chunk-common', 'web']
		}
	},
	lintOnSave: false,
	pluginOptions: {
		electronBuilder: {
			nodeIntegration: true
		}
	},
	chainWebpack: (config) => {
		config.plugins.delete('prefetch')
		config.plugin('define').tap((args) => {
			args[0].CURRENT_VERSION = JSON.stringify(actualVersion)
			args[0].SYSTEM_ARCH = JSON.stringify(process.arch)
			return args
		})
	},
	configureWebpack: (config) => {
		config.resolve.alias['@'] = resolve('src')
		config.resolve.alias['_c'] = resolve('src/components')
		config.resolve.alias['@store'] = resolve('src/store')
	},
	devServer: {
		host: '0.0.0.0',
		port: '8840',
		proxy: {
			'/sundun-edas': {
				target: 'http://************:8840',
				changeOrigin: true,
				pathRewrite: {
					'^/sundun-edas': '/sundun-edas'
				}
			},
			'/bsp-uac': {
				// target: 'http://**************:1910',
				target: 'http://*************:1910',
				changeOrigin: true,
				pathRewrite: {
					'^/bsp-uac': '/'
				}
			},
			'/ptm-com': {
				// target: 'http://**************:1910',
				target: 'http://*************:9400',
				changeOrigin: true,
				pathRewrite: {
					'^/ptm-com': '/'
				}
			},
			'/pym-upload': {
				target: 'http://**************:8840',
				changeOrigin: true,
				pathRewrite: {
					'^/pym-upload': '/pym-upload'
				}
			},
			'/pym-bus|/pym-sys': {
				target: 'http://*************:8190',
				changeOrigin: true,
				pathRewrite: {
					'^/pym-sys': '/',
					'^/pym-bus': '/'
				}
			},
			'/sundun-face': {
				// target: 'http://***********:5000', // Linux 高新兴、百度云服务
				target: 'http://192.168.3.211:22',
				pathRewrite: {
					'^/sundun-face': '/'
				}
			},
			'/pureWeb': {
				target: 'http://127.0.0.1:9527',
				changeOrigin: true,
				pathRewrite: {
					'^/pureWeb': '/pureWeb'
				}
			}
		}
	}
}

module.exports = config
