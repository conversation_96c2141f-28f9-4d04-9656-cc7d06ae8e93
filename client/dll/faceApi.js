const path = require('path')
const ffi = require('ffi-napi')
const ref = require('ref-napi')
const StructType = require('ref-struct-di')(ref)
const ArrayType = require('ref-array-di')(ref)
const { ipcMain, app } = require('electron')
const Logger = require('../log/index')
const log = new Logger('face')

class FaceApi {
	constructor() {
		this.params = {
			resourcesPath: '',
			photoshopMode: ''
		}
		this.extraResources = null
		this.faceMethods = null
		this.faceId = null
		/**
		 * 人脸检测参数信息
		 * format：图像格式, RGB888 = 0 , BGR888 = 1 , RGBX8888 = 2 , BGRX8888 = 3 , YUV420P = 4 , I420 = YUV420P , NV12 = 5 , NV21 = 6
		 */
		this._SD_IMAGE = StructType({
			format: ref.types.int,
			planeDatas: ArrayType(ref.refType(ref.types.uchar), 4),
			planeStrides: ArrayType(ref.types.int, 4),
			width: ref.types.int,
			height: ref.types.int
		})
		// 实际人脸区域信息
		this._FACE_INFO = StructType({
			x: ref.types.int,
			y: ref.types.int,
			width: ref.types.int,
			height: ref.types.int,
			faceId: ref.types.int
		})
	}
	/**
	 * 初始化人脸检测方法
	 */
	_initFaceMethods() {
		const detectorID = ref.refType(ref.types.int32)
		const faceInfo = ref.refType(this._FACE_INFO)
		if (process.platform == 'win32') {
			process.env.PATH += `${path.delimiter}${path.join(this.extraResources, this.params.resourcesPath.substring(0, this.params.resourcesPath.lastIndexOf('/')))}`
		}
		const libPath = path.join(this.extraResources, this.params.resourcesPath)
		this.faceMethods = new ffi.Library(libPath, {
			// SDK全局初始化
			SDFACESDK_Init: ['int', ['string', 'bool']],
			// 创建人脸检测器
			SDFACESDK_CreateDetector: ['int', [detectorID, 'int']],
			// 配置检测器
			SDFACESDK_FinishConfig: ['int', ['int']],
			// 人脸检测
			SDFACESDK_DetectMaxFaceByBase64: ['int', ['int', 'string', 'int', 'string', faceInfo]],
			// 销毁检测器
			SDFACESDK_DestroyDetector: ['int', ['int']],
			// SDK全局反初始化
			SDFACESDK_UnInit: ['int', []]
		})
		log.info('初始指针方法完成')
	}
	/**
	 * 初始化SDK
	 */
	_initFace() {
		const ret = this.faceMethods.SDFACESDK_Init(this.params.resourcesPath.split('/')[0], true)
		log.info(ret, '人脸初始化结果')
		return ret
	}
	/**
	 * 创建人脸检测器
	 */
	_createFaceRecognition(photoshopMode) {
		const outDetectorID = ref.alloc('int')
		const ret = this.faceMethods.SDFACESDK_CreateDetector(outDetectorID, Number(photoshopMode))
		log.info(ret, '创建人脸检测器结果')
		this.faceId = ret == 0 ? outDetectorID.deref() : null
		return ret
	}
	/**
	 * 配置人脸检测器
	 */
	_configFaceRecognition() {
		const ret = this.faceMethods.SDFACESDK_FinishConfig(this.faceId)
		log.info(ret, '配置人脸检测器结果')
		return ret
	}
	/**
	 * 人脸检测
	 */
	_detectFaceRecognition(imageData) {
		if (this.faceId === null) this._createFaceRecognition(this.params.photoshopMode)
		// 初始化一个指向 this._FACE_INFO 类的指针, 一个引用对象
		const faceImagData = ref.alloc(this._FACE_INFO)
		try {
			const ret = this.faceMethods.SDFACESDK_DetectMaxFaceByBase64(this.faceId, imageData, imageData.length, '', faceImagData)
			log.info(ret, '人脸识别结果')
			if (ret === 0) {
				// deref() 获取指针实例对应的值
				return faceImagData.deref()
			} else {
				return null
			}
		} catch (e) {
			log.info(e.toString(), '人脸识别错误')
			return null
		}
	}
	/**
	 * 销毁检测器 && 反初始化SDK
	 */
	_destoryDetector() {
		if (this.faceId != null) {
			this.faceMethods.SDFACESDK_DestroyDetector(this.faceId)
			this.faceMethods.SDFACESDK_UnInit()
		}
	}
	/**
	 * 初始化人脸检测器
	 */
	initDev(photoshopMode) {
		if (photoshopMode == this.params.photoshopMode && this.faceId != null) {
			log.info('已初始化了')
		} else {
			log.info('人脸id：', this.faceId)
			if (this.faceId != null) {
				// 销毁原先的检测器
				this._destoryDetector()
			}
			if (this._initFace() != 0) {
				log.info('初始化人脸检测器失败')
				return
			}
			if (this._createFaceRecognition(photoshopMode) != 0) {
				log.info('创建人脸检测器失败')
				return
			}
			this.params.photoshopMode = photoshopMode
			if (this._configFaceRecognition() != 0) {
				log.info('配置人脸检测器失败')
			}
		}
	}
	/**
	 * 销毁监听事件
	 */
	unInitDev() {
		this._destoryDetector()
		ipcMain.removeAllListeners('init_face_config')
		ipcMain.removeAllListeners('face_recognition_path')
		ipcMain.removeAllListeners('destory_face_detector')
	}
	/**
	 * 初始化, 当参数值更改时，先 unInitDev(), 再重新初始化
	 */
	init() {
		/**
		 * params.resourcesPath 资源文件 dll/so 路径（相对于项目下 extraResources 文件夹的路径）
		 * params.photoshopMode 算法模式
		 */
		ipcMain.on('init_face_config', (event, params) => {
			if (this.faceId == null) {
				// 销毁原先的检测器
				this._destoryDetector()
			}
			if (params.photoshopMode == -1) {
				log.info('人脸检测算法不能为空')
				return
			}
			const faceResourcesPathMap = {
				arm64: '/face_sdk/arm64/libSdFaceSDK.so',
				x64: '/face_sdk/amd64/libSdFaceSDK.so',
				ia32: '/face_sdk/win32/SdFaceSDK.dll'
			}
			this.extraResources = path.join(app.isPackaged ? process.resourcesPath : process.cwd(), 'extraResources')
			this.params.resourcesPath = faceResourcesPathMap[process.arch]
			log.info('资源文件路径', this.extraResources + this.params.resourcesPath)
			this._initFaceMethods()
			this.initDev(params.photoshopMode)
		})
		/**
		 * params
		 * params.base64 base64图片
		 */
		ipcMain.on('face_recognition_path', (event, params) => {
			if (!params.base64) {
				log.info('图片信息不能为空！', '人脸识别错误')
				event.returnValue = null
				return
			}
			try {
				const resultImagData = this._detectFaceRecognition(params.base64.replace(/^data:image\/\w+;base64,/, ''))
				if (resultImagData) {
					const { x, y, width, height } = resultImagData
					event.returnValue = { x, y, width, height }
				}
				event.returnValue = null
			} catch (e) {
				log.info(e, '人脸识别错误')
				event.returnValue = null
			}
		})
		/**
		 * 销毁人脸检测容器
		 */
		ipcMain.on('destory_face_detector', () => {
			this._destoryDetector()
		})
	}
}

module.exports = FaceApi
