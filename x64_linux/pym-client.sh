#!/bin/sh

#安装路径
CURRENT_DIR=$(dirname `readlink -f $0`)

# 获取CPU架构
cpu_arch=$(uname -m)

# 初始化LD_LIBRARY_PATH为空，以防之前已经有设置
LD_LIBRARY_PATH=""

if [ "$cpu_arch" = "x86_64" ]; then
	LD_LIBRARY_PATH="${CURRENT_DIR}/resources/extraResources/face_sdk/amd64:${CURRENT_DIR}/resources/extraResources/face_sdk/amd64/lib:${CURRENT_DIR}/resources/extraResources/face_sdk/amd64/GxxFaceRecognition/UOS_x86_64:$LD_LIBRARY_PATH"
elif [ "$cpu_arch" = "aarch64" ]; then
	LD_LIBRARY_PATH="${CURRENT_DIR}/resources/extraResources/face_sdk/arm64:${CURRENT_DIR}/resources/extraResources/face_sdk/arm64/lib:${CURRENT_DIR}/resources/extraResources/face_sdk/arm64/GxxFaceRecognition/UOS_aarch64:$LD_LIBRARY_PATH"
else
	echo "Unsupported CPU architecture: $cpu_arch"
	exit 1
fi

# 导出LD_LIBRARY_PATH环境变量
export LD_LIBRARY_PATH

# 启动程
${CURRENT_DIR}/pym-client
