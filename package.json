{"name": "pym-client", "version": "5.0.3", "smallVersion": "4", "actualVersion": "*******.2024.07.12.0936", "description": "Terminal of property management cabinet", "private": true, "homepage": "", "email": "", "license": "MIT", "scripts": {"build:web": "vue-cli-service build --web", "build:win-exe": "npm run update-version && npm run build:web && electron-builder --win", "build:linux-amd64-deb": "electron-builder --linux && npm run mkdeb", "build:linux-arm64-deb": "electron-builder --dir --arm64 --linux && npm run mkdeb", "dev:web": "vue-cli-service serve --web", "dev:win": "chcp 65001 && electron .", "dev:linux-amd64": "LD_LIBRARY_PATH=${LD_LIBRARY_PATH:=./extraResources/face_sdk/amd64:./extraResources/face_sdk/amd64/lib:./extraResources/face_sdk/amd64/GxxFaceRecognition/UOS_x86_64} electron .", "dev:linux-arm64": "LD_LIBRARY_PATH=${LD_LIBRARY_PATH:=./extraResources/face_sdk/arm64:./extraResources/face_sdk/arm64/lib:./extraResources/face_sdk/arm64/GxxFaceRecognition/UOS_aarch64} electron .", "lint": "vue-cli-service lint", "mkdeb": "cd ./x64_linux && sudo chmod +x ./*.sh && sed -i 's/\r$//' ./*.rules ./*.sh ./scripts/* && ./mkdeb.sh", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps", "rebuild": "electron-rebuild", "update-version": "node ./build-terminal-version.js"}, "main": "main.js", "bugs": {"url": "https://github.com/owner/project/issues", "email": "<EMAIL>"}, "author": {"name": "b@rubb", "email": "<EMAIL>", "url": "https://github.com/owner/project/issues"}, "contributors": [{"name": "<EMAIL>", "email": "<EMAIL>", "url": "https://github.com/owner/project/issues"}], "build": {"appId": "5034202407120936", "productName": "pym-client", "copyright": "gosuncn", "asar": true, "directories": {"output": "./dist_electron"}, "files": ["main.js", "auth_window.js", "client/**/*", "dist/**/*"], "extraResources": [{"from": "./extraResources/", "to": "extraResources"}, {"from": "./x64_linux/pym-client.sh", "to": "../pym-client.sh"}, {"from": "./public/icon.png", "to": "./icon.png"}, {"from": "./public/icon.ico", "to": "./icon.ico"}], "win": {"icon": "./public/icon.ico", "artifactName": "pym-client_ia32_*******.2024.07.12.0936.exe", "target": [{"target": "nsis", "arch": ["ia32"]}]}, "linux": {"icon": "./public/icon.png", "category": "Utility", "target": ["deb"]}, "nsis": {"oneClick": true, "allowElevation": true, "allowToChangeInstallationDirectory": false, "installerIcon": "./public/install.ico", "uninstallerIcon": "./public/install.ico", "installerHeaderIcon": "./public/install.ico", "installerSidebar": "./public/siderbar.bmp", "uninstallerSidebar": "./public/siderbar.bmp", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "附物管理终端"}}, "dependencies": {"archiver": "^5.2.0", "axios": "^0.21.4", "core-js": "^3.8.3", "crypto-js": "^4.0.0", "electron-log": "^4.4.1", "express": "^4.19.2", "ffi-napi": "4.0.3", "fs-extra": "^11.2.0", "gm-crypto": "^0.1.12", "gosuncn-ui": "^1.0.32", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "jsencrypt": "^3.3.2", "jsrsasign": "^10.8.6", "moment": "^2.29.4", "mqtt": "^4.3.8", "nodejs-websocket": "^1.7.2", "ping": "^0.4.4", "python": "^0.0.4", "qs": "^6.10.1", "ref-array-di": "^1.2.2", "ref-struct-di": "^1.1.1", "request": "^2.88.2", "serialport": "^10.5.0", "serve-static": "^1.15.0", "systeminformation": "^5.22.8", "vconsole": "^3.15.1", "view-design": "^4.7.0", "vue": "^2.6.14", "vue-router": "~3.5.2", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-polyfill": "^6.26.0", "css-loader": "^5.2.4", "electron": "^12.0.9", "electron-builder": "^23.6.0", "electron-rebuild": "^1.11.0", "eslint": "7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-loader": "^4.0.2", "eslint-plugin-import": "^2.23.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^7.15.1", "less": "^4.1.2", "less-loader": "^4.1.0", "postcss-plugin-px2rem": "^0.8.1", "prettier": "2.3.2", "style-loader": "^2.0.0", "style-resources-loader": "^1.4.1", "stylus": "^0.54.8", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}